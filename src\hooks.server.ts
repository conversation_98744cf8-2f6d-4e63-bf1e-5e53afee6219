import type { Handle } from '@sveltejs/kit';
import { verifyJWT } from '$lib/server/auth.js';
import { getSessionByToken, getUserById } from '$lib/server/db/operations.js';

// Initialize admin check on startup
let adminInitialized = false;

async function initializeAdmin() {
  if (adminInitialized) return;

  try {
    // Import admin initialization functions
    const { getAdminUser, getUserByEmail, createUserByAdmin, updateUserAdminStatus } = await import('$lib/server/db/operations.js');
    const { generateRandomPassword, hashPassword } = await import('$lib/server/auth.js');
    const { sendEmail } = await import('$lib/server/email.js');

    const adminEmail = process.env.ADMIN_EMAIL;

    if (!adminEmail) {
      console.log('ADMIN_EMAIL not configured, skipping admin initialization');
      adminInitialized = true;
      return;
    }

    // Check if admin already exists
    const existingAdmin = await getAdminUser();
    if (existingAdmin) {
      console.log(`Admin already exists: ${existingAdmin.email}`);
      adminInitialized = true;
      return;
    }

    // Check if user with admin email exists
    let adminUser = await getUserByEmail(adminEmail);

    if (adminUser) {
      // User exists, just make them admin
      await updateUserAdminStatus(adminUser.id, true);
      console.log(`Existing user promoted to admin: ${adminUser.email}`);
    } else {
      // Create new admin user
      const password = generateRandomPassword();
      const hashedPassword = await hashPassword(password);

      adminUser = await createUserByAdmin({
        email: adminEmail,
        name: 'Administrator',
        password: hashedPassword,
        isAdmin: true
      });

      console.log(`Admin account created for ${adminEmail} with password: ${password}`);

      // Try to send email, but don't fail if it doesn't work
      try {
        const emailHtml = `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background: linear-gradient(135deg, #4299e1, #63b3ed); padding: 2rem; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 1.75rem;">Routine Mail</h1>
            </div>

            <div style="padding: 2rem; background: white;">
              <h2 style="color: #2d3748; margin-bottom: 1rem;">Admin Account Created</h2>

              <p style="color: #4a5568; line-height: 1.6; margin-bottom: 1rem;">
                Your admin account has been automatically created for Routine Mail. Here are your login credentials:
              </p>

              <div style="background: #f7fafc; border: 2px solid #e2e8f0; border-radius: 8px; padding: 1.5rem; margin: 1.5rem 0;">
                <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Email:</strong> ${adminEmail}</p>
                <p style="margin: 0.5rem 0; color: #2d3748;"><strong>Password:</strong> <code style="background: #e2e8f0; padding: 0.25rem 0.5rem; border-radius: 4px;">${password}</code></p>
              </div>

              <p style="color: #e53e3e; line-height: 1.6; margin-bottom: 1rem;">
                <strong>Important:</strong> Please log in and change your password immediately for security.
              </p>

              <div style="text-align: center; margin: 2rem 0;">
                <a href="${process.env.APP_URL || 'http://localhost:5173'}/login"
                   style="background: #4299e1; color: white; padding: 0.75rem 1.5rem; text-decoration: none; border-radius: 6px; display: inline-block;">
                  Login as Admin
                </a>
              </div>

              <div style="border-top: 1px solid #e2e8f0; padding-top: 1.5rem; margin-top: 2rem;">
                <p style="color: #718096; font-size: 0.875rem; margin: 0;">
                  This is an automated message from Routine Mail. Please do not reply to this email.
                </p>
              </div>
            </div>
          </div>
        `;

        const emailSent = await sendEmail(
          adminEmail,
          'Routine Mail - Admin Account Created',
          emailHtml
        );

        if (emailSent) {
          console.log(`Admin credentials sent to ${adminEmail}`);
        } else {
          console.error(`Failed to send admin credentials to ${adminEmail}`);
        }
      } catch (emailError) {
        console.error('Failed to send admin credentials email:', emailError);
      }
    }
  } catch (error) {
    console.error('Admin initialization error:', error);
  } finally {
    adminInitialized = true;
  }
}
import { taskScheduler } from '$lib/server/scheduler.js';

// Initialize task scheduler on server startup
if (!taskScheduler.getStatus().isRunning) {
  taskScheduler.start();
}

export const handle: Handle = async ({ event, resolve }) => {
  // Initialize admin on first request
  if (!adminInitialized) {
    initializeAdmin();
  }

  const token = event.cookies.get('auth-token');

  if (token) {
    try {
      // Verify JWT
      const payload = verifyJWT(token);
      if (payload) {
        // Check if session exists in database
        const session = await getSessionByToken(token);
        if (session && session.expiresAt > new Date()) {
          // Get user data
          const user = await getUserById(session.userId);
          if (user) {
            event.locals.user = {
              id: user.id,
              email: user.email,
              name: user.name,
              isVerified: user.isVerified,
              isAdmin: user.isAdmin || false,
              isSuspended: user.isSuspended || false,
              emailRemindersEnabled: user.emailRemindersEnabled,
              timezone: user.timezone,
              emailFrequencyDays: user.emailFrequencyDays,
              emailPreviewDays: user.emailPreviewDays
            };
          }
        }
      }
    } catch (error) {
      console.error('Auth middleware error:', error);
      // Clear invalid token
      event.cookies.delete('auth-token', { path: '/' });
    }
  }

  return resolve(event);
};
